// ignore_for_file: use_build_context_synchronously
import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:window_manager/window_manager.dart';
import 'package:path/path.dart' as path;

class AppConfig {
  late String taskTitle;
  late String sourcePath;
  late String destinationPath;
  late bool overwrite;
  late DateTime scheduledTime;
  late Frequency selectedFrequency;

  AppConfig({
    required this.taskTitle,
    required this.sourcePath,
    required this.destinationPath,
    required this.overwrite,
    required this.scheduledTime,
    required this.selectedFrequency,
  });

  // Convert AppConfig object to JSON format
  Map<String, dynamic> toJson() {
    return {
      'taskTitle': taskTitle,
      'sourcePath': sourcePath,
      'destinationPath': destinationPath,
      'overwrite': overwrite,
      'scheduledTime': scheduledTime.toIso8601String(),
      'selectedFrequency': selectedFrequency.toString().split('.').last,
    };
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await windowManager.ensureInitialized();

  WindowOptions windowOptions = const WindowOptions(
    title: 'AutoCopy',
    size: Size(600, 850),
    center: true,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    windowButtonVisibility: true,
  );
  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.setResizable(true);
    await windowManager.setMaximizable(false);
    await windowManager.show();
    await windowManager.focus();
  });

  runApp(const AutoCopyApp());
}

enum Frequency { minute, hourly, daily, weekly, monthly, once }

enum CopyStatus { success, failed, skipped }

class AutoCopyApp extends StatelessWidget {
  const AutoCopyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ThemeNotifier(),
      child: Consumer<ThemeNotifier>(
        builder: (context, notifier, child) {
          return PopScope(
            canPop: false,
            onPopInvoked: (didPop) async {
              if (didPop) {
                final shouldExit = await showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Exit AutoCopy'),
                    content: const Text('Are you sure you want to exit?'),
                    actions: [
                      TextButton(
                        onPressed: () =>
                            Navigator.pop(context, false), // Cancel
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () =>
                            Navigator.pop(context, true), // Confirm
                        child: const Text('Exit'),
                      ),
                    ],
                  ),
                );
                return Navigator.pop(
                    context, shouldExit ?? false); // Default to not exiting
              }
            },
            child: MaterialApp(
              title: 'AutoCopy',
              theme: notifier.darkMode ? ThemeData.dark() : ThemeData.light(),
              home: const AutoCopyScreen(),
            ),
          );
        },
      ),
    );
  }
}

class AutoCopyScreen extends StatefulWidget {
  const AutoCopyScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _AutoCopyScreenState createState() => _AutoCopyScreenState();
}

class _AutoCopyScreenState extends State<AutoCopyScreen> {
  late TextEditingController _taskTitleController;
  String _taskTitle = '';
  late TextEditingController _sourcePathController;
  late TextEditingController _destinationPathController;
  bool canupdated = false;
  String _sourcePath = '';
  String _destinationPath = '';
  bool _overwrite = false;
  final List<LogItem> _logs = [];
  DateTime _scheduledTime = DateTime.now();
  bool _copyStarted = false;
  Frequency _selectedFrequency = Frequency.daily;
  Timer? _timerSubscription;
  void _addLog(String sourcePath, DateTime copyDate,
      {bool copyStatus = true,
      bool skipped = false,
      required String errorMessage}) {
    setState(() {
      if (skipped) {
        _logs.add(LogItem(
          sourcePath: sourcePath,
          copyDate: copyDate,
          copyStatus: CopyStatus.skipped,
        ));
      } else {
        _logs.add(LogItem(
          sourcePath: sourcePath,
          copyDate: copyDate,
          copyStatus: copyStatus ? CopyStatus.success : CopyStatus.failed,
        ));
      }
    });
  }

// Function to save logs to a JSON file
  Future<void> _saveLogsToFile() async {
    final List<Map<String, dynamic>> logsJson =
        _logs.map((log) => log.toJson()).toList();
    final jsonString = json.encode(logsJson);
    final file = File('logs.json');
    await file.writeAsString(jsonString);
  }

// Function to load logs from a JSON file
  Future<void> _loadLogsFromFile() async {
    try {
      final file = File('logs.json');
      final jsonString = await file.readAsString();
      final List<dynamic> logsJson = json.decode(jsonString);
      setState(() {
        _logs.clear();
        _logs.addAll(
          logsJson.map((json) => LogItem.fromJson(json)).toList(),
        );
      });
    } catch (e) {
      // Handle file read error or missing file
    }
  }

  Future<void> _saveConfigToFile() async {
    final file = File('config.json');

    // Check if the file exists before writing
    if (await file.exists()) {
      await file.writeAsString(json.encode(AppConfig(
        taskTitle: _taskTitle,
        sourcePath: _sourcePath,
        destinationPath: _destinationPath,
        overwrite: _overwrite,
        scheduledTime: _scheduledTime,
        selectedFrequency: _selectedFrequency,
      ).toJson()));
    } else {
      // Create the file if it doesn't exist
      await file.create();
      // Then write the data
      await file.writeAsString(json.encode(AppConfig(
        taskTitle: _taskTitle,
        sourcePath: _sourcePath,
        destinationPath: _destinationPath,
        overwrite: _overwrite,
        scheduledTime: _scheduledTime,
        selectedFrequency: _selectedFrequency,
      ).toJson()));
    }
  }

  Future<void> checkForUpdates() async {
    try {
      String updateSourceFolder =
          r'\\10.10.3.116\IT Share$\AutoCopy\AutoCopy New Tech';
      // Check if the update source folder exists
      if (await Directory(updateSourceFolder).exists()) {
        // Get a list of files in the update source folder
        List<FileSystemEntity> updateFiles =
            await Directory(updateSourceFolder).list().toList();

        // Loop through each file in the update source folder
        for (var file in updateFiles) {
          // Check if the file is a File object
          if (file is File) {
            // Get the filename and destination path
            String filename = file.path.split(Platform.pathSeparator).last;
            String destinationPath = '${Directory.current.path}/$filename';
            File destinationFile = File(destinationPath);

            DateTime sourceModified = await file.lastModified();
            DateTime destinationModified = await destinationFile.lastModified();

            if (sourceModified.isAfter(destinationModified)) {
              canupdated = true; // Set canUpdate to true if source is newer
              break; // Exit the loop if any file is newer
            }
          }
        }

        if (canupdated) {
          // Show update available message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Update available!'),
            ),
          );
        } else {
          // Show no update available message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No update available.'),
            ),
          );
        }
      } else {
        // Show folder not found message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Update source folder not found.'),
          ),
        );
      }
    } catch (e) {
      // Show generic error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('An error occurred while checking for updates.'),
        ),
      );
    }
  }

  Text _selectedScheduledTimeLabel(DateTime selectedTime) {
    // Use DateFormat to format the date without seconds and milliseconds
    final formattedTime = DateFormat('yyyy-MM-dd HH:mm').format(selectedTime);
    return Text(
      'Start Time: $formattedTime',
      style: const TextStyle(fontWeight: FontWeight.bold),
    );
  }

  @override
  void initState() {
    _sourcePathController = TextEditingController(text: _sourcePath);
    _destinationPathController = TextEditingController(text: _destinationPath);
    _taskTitleController = TextEditingController(text: _taskTitle);
    Future.delayed(const Duration(seconds: 2), () {
      // Delay for 2 seconds
      _loadConfigFromFile();
      _loadLogsFromFile();
      checkForUpdates();
    });
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _loadConfigFromFile();
      await _loadLogsFromFile();
      await checkForUpdates();
    });
  }

  @override
  void dispose() {
    _cancelTimerSubscription();
    _taskTitleController.dispose();
    _sourcePathController.dispose();
    _destinationPathController.dispose();
    _saveConfigToFile(); // Save config data to file on dispose
    super.dispose();
  }

  Future<void> updateFiles() async {
    String vbProgramPath = 'Updater.bat'; // Adjust the path accordingly

    // Trigger the execution of the VB.NET program without passing any arguments
    Process.start(vbProgramPath, []).then((Process process) {
      // Handle process completion or errors if needed
    }).catchError((error) {
      //print('Error executing VB.NET program: $error');
    });
  }

// Load config data from file
  Future<void> _loadConfigFromFile() async {
    try {
      final file = File('config.json');
      final jsonContent = await file.readAsString();
      final jsonData = json.decode(jsonContent);

      setState(() {
        _sourcePath = jsonData['sourcePath'];
        _sourcePathController.text = _sourcePath;

        _destinationPath = jsonData['destinationPath'];
        _destinationPathController.text = _destinationPath;
        _overwrite = jsonData['overwrite'];
        _scheduledTime = DateTime.parse(jsonData['scheduledTime']);
        _selectedFrequency = Frequency.values.firstWhere((e) =>
            e.toString().split('.').last == jsonData['selectedFrequency']);
        _taskTitle = jsonData['taskTitle'];
        _taskTitleController.text = _taskTitle;
      });
    } catch (e) {
      // Handle file read error or missing file
    }
  }

  Future<void> _startTimer() async {
    if (_timerSubscription == null) {
      Duration interval = _getIntervalFromFrequency(_selectedFrequency);
      DateTime now = DateTime.now();
      DateTime nextScheduledTime = _getNextScheduledTime(now, interval);
      if (DateTime.now().isAfter(_scheduledTime) ||
          DateTime.now().isAtSameMomentAs(_scheduledTime)) {
        await _copyFiles();
      }
      // Calculate the delay until the next scheduled time
      Duration delay = nextScheduledTime.difference(now);
      // Schedule the copy process to start at the specified time using Timer
      Timer(delay, () async {
        await _copyFiles();
        _startPeriodicTimer(
            interval); // Start periodic timer for subsequent copies
      });
    }
  }

  void _startPeriodicTimer(Duration interval) {
    _timerSubscription = Timer.periodic(interval, (timer) async {
      await _copyFiles();
    });
  }

  DateTime _getNextScheduledTime(DateTime now, Duration interval) {
    switch (_selectedFrequency) {
      case Frequency.minute:
        return DateTime(now.year, now.month, now.day, _scheduledTime.hour,
                _scheduledTime.minute)
            .add(const Duration(minutes: 1));
      case Frequency.hourly:
        return DateTime(now.year, now.month, now.day, _scheduledTime.hour,
                _scheduledTime.minute)
            .add(const Duration(hours: 1));
      case Frequency.daily:
        return DateTime(now.year, now.month, now.day + 1, _scheduledTime.hour,
            _scheduledTime.minute);
      case Frequency.weekly:
        return DateTime(now.year, now.month, now.day + 7, _scheduledTime.hour,
            _scheduledTime.minute);
      case Frequency.monthly:
        return DateTime(now.year, now.month + 1, _scheduledTime.day,
            _scheduledTime.hour, _scheduledTime.minute);
      default:
        return now;
    }
  }

  void _stopTimer() {
    _cancelTimerSubscription();
    _cancelCopyProcess();
  }

  void _cancelCopyProcess() {
    // If there is an ongoing copy process, cancel it
    // This will stop the copy process immediately
    if (_copyStarted) {
      _copyStarted = false;
      // Cancel any ongoing file copy operation
      // You can implement logic to cancel ongoing file copying here
      // For example, you can cancel any async file copy operations
      // or set a flag to stop copying files in the next iteration of _copyFiles() method
    }
  }

  void _cancelTimerSubscription() {
    if (_timerSubscription != null) {
      _timerSubscription!.cancel();
      _timerSubscription = null;
    }
  }

  Duration _getIntervalFromFrequency(Frequency frequency) {
    switch (frequency) {
      case Frequency.minute:
        return const Duration(minutes: 1);
      case Frequency.hourly:
        return const Duration(hours: 1);
      case Frequency.daily:
        return const Duration(days: 1);
      case Frequency.weekly:
        return const Duration(days: 7);
      case Frequency.monthly:
        return const Duration(days: 30); // Adjust for different month lengths
      default:
        return Duration.zero;
    }
  }

  bool _fileCopied = false;
  Future<void> _copyFiles() async {
    Directory sourceDir = Directory(_sourcePath);
    Directory destinationDir = Directory(_destinationPath);
    try {
      if (await sourceDir.exists() && await destinationDir.exists()) {
        await for (var entity in sourceDir.list()) {
          await _copyEntity(entity, destinationDir, "", overwrite: _overwrite);
        }
        if (_fileCopied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Copy completed successfully!')),
          );
        }
      } else {
        // Handle source or destination directory not found
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Source or destination directory not found!'),
          ),
        );
      }
    } catch (error) {
      return;
    } finally {}
  }

  Future<void> _copyEntity(
      FileSystemEntity entity, Directory destinationDir, String relativePath,
      {required bool overwrite}) async {
    if (entity is File) {
      String newPath = path.join(
        destinationDir.path,
        relativePath,
        path.basename(entity.path),
      );

      if (!overwrite) {
        // Check if file exists
        File destinationFile = File(newPath);
        if (await destinationFile.exists()) {
          // Compare modification dates if overwrite is false
          DateTime sourceModified = await entity.lastModified();
          DateTime destinationModified = await destinationFile.lastModified();
          if (sourceModified.isAtSameMomentAs(destinationModified)) {
            _addLog(newPath, DateTime.now(),
                copyStatus: false, skipped: true, errorMessage: "");
            _saveLogsToFile(); // Log skipped file with path
            return;
          }
        }
      }
      try {
        await entity.copy(newPath);
        _addLog(newPath, DateTime.now(),
            copyStatus: true, skipped: false, errorMessage: "");
        _saveLogsToFile(); // Log successful copy with full path
        _fileCopied = true;
      } catch (error) {
        String errorMessage = error.toString(); // Get the error message
        _addLog(entity.path, DateTime.now(),
            copyStatus: false, skipped: false, errorMessage: errorMessage);
        _saveLogsToFile(); // Log failed copy with full path
      }
    } else if (entity is Directory) {
      String newDirPath = path.join(
        destinationDir.path,
        relativePath,
        path.basename(entity.path),
      );

      if (!await Directory(newDirPath).exists()) {
        await Directory(newDirPath).create(recursive: true);
      }

      // Copy the contents of the directory
      await for (var subEntity in entity.list()) {
        await _copyEntity(
          subEntity,
          Directory(newDirPath),
          '',
          overwrite: overwrite,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AutoCopy'),
        actions: [
          TextButton(
              onPressed: () {
                checkForUpdates();
                if (canupdated) {
                  updateFiles();
                }
              },
              child:
                  Text(canupdated ? 'Update Available' : 'Check for Update')),
          TextButton(
            onPressed: () {
              _loadLogsFromFile();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LogScreen(logs: _logs),
                ),
              );
            },
            child: const Text('Log'),
          ),
          const SizedBox(width: 5.0), // Add some horizontal spacing
          IconButton(
            icon: Icon(
              Provider.of<ThemeNotifier>(context).darkMode
                  ? Icons.brightness_2
                  : Icons.brightness_7,
            ),
            onPressed: () {
              Provider.of<ThemeNotifier>(context, listen: false).toggleTheme();
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _taskTitleController,
              onChanged: (value) {
                setState(() {
                  _taskTitle = value;
                });
              },
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                labelText: 'Task Title',
                labelStyle: TextStyle(
                  color: Colors.cyan,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: const TextStyle(
                color: Colors.cyan,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Source Path',
              ),
              controller: _sourcePathController,
              onChanged: (value) {
                setState(() {
                  _sourcePath = value;
                });
              },
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            ElevatedButton(
              style: ButtonStyle(
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ))),
              onPressed: () async {
                String? source = await FilePicker.platform.getDirectoryPath();
                if (source != null) {
                  setState(() {
                    _sourcePath = source;
                    _sourcePathController.text = _sourcePath;
                  });
                }
              },
              child: const Text('Pick Source Folder'),
            ),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Destination Path',
              ),
              controller: _destinationPathController,
              onChanged: (value) {
                setState(() {
                  _destinationPath = value;
                });
              },
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            ElevatedButton(
              style: ButtonStyle(
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ))),
              onPressed: () async {
                String? destination =
                    await FilePicker.platform.getDirectoryPath();
                if (destination != null) {
                  setState(() {
                    _destinationPath = destination;
                    _destinationPathController.text = _destinationPath;
                  });
                }
              },
              child: const Text('Pick Destination Folder'),
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            SwitchListTile(
              title: const Text('Overwrite'),
              value: _overwrite,
              onChanged: (value) {
                setState(() {
                  _overwrite = value;
                });
              },
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _selectedScheduledTimeLabel(_scheduledTime),
                const Padding(padding: EdgeInsets.all(5.0)),
                const Text(
                  'Frequency:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                RadioListTile<Frequency>(
                  title: const Text('Minute'),
                  value: Frequency.minute,
                  groupValue: _selectedFrequency,
                  onChanged: (value) {
                    setState(() {
                      _selectedFrequency = value!;
                    });
                  },
                ),
                RadioListTile<Frequency>(
                  title: const Text('Hourly'),
                  value: Frequency.hourly,
                  groupValue: _selectedFrequency,
                  onChanged: (value) {
                    setState(() {
                      _selectedFrequency = value!;
                    });
                  },
                ),
                RadioListTile<Frequency>(
                  title: const Text('Daily'),
                  value: Frequency.daily,
                  groupValue: _selectedFrequency,
                  onChanged: (value) {
                    setState(() {
                      _selectedFrequency = value!;
                    });
                  },
                ),
                RadioListTile<Frequency>(
                  title: const Text('Weekly'),
                  value: Frequency.weekly,
                  groupValue: _selectedFrequency,
                  onChanged: (value) {
                    setState(() {
                      _selectedFrequency = value!;
                    });
                  },
                ),
                RadioListTile<Frequency>(
                  title: const Text('Monthly'),
                  value: Frequency.monthly,
                  groupValue: _selectedFrequency,
                  onChanged: (value) {
                    setState(() {
                      _selectedFrequency = value!;
                    });
                  },
                ),
              ],
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            ElevatedButton(
              style: ButtonStyle(
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ))),
              onPressed: () async {
                final selectedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2100),
                );
                if (selectedDate != null) {
                  final selectedTime = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(DateTime.now()),
                  );
                  if (selectedTime != null) {
                    setState(() {
                      _scheduledTime = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                        selectedTime.hour,
                        selectedTime.minute,
                      );
                    });
                  }
                }
              },
              child: const Text('Pick Scheduled Time'),
            ),
            const Padding(padding: EdgeInsets.all(5.0)),
            ElevatedButton(
              style: ButtonStyle(
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                ),
              ),
              onPressed: () async {
                if (_copyStarted) {
                  final confirmation = await showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Stop Copy'),
                      content:
                          const Text('Are you sure you want to stop copying?'),
                      actions: [
                        TextButton(
                          onPressed: () =>
                              Navigator.pop(context, false), // Cancel
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () =>
                              Navigator.pop(context, true), // Confirm
                          child: const Text('Stop'),
                        ),
                      ],
                    ),
                  );
                  if (confirmation ?? false) {
                    setState(() {
                      _copyStarted = false;
                      _stopTimer();
                    });
                  }
                } else {
                  final confirmation = await showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Start Copy'),
                      content:
                          const Text('Are you sure you want to start copying?'),
                      actions: [
                        TextButton(
                          onPressed: () =>
                              Navigator.pop(context, false), // Cancel
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () =>
                              Navigator.pop(context, true), // Confirm
                          child: const Text('Start'),
                        ),
                      ],
                    ),
                  );
                  if (confirmation ?? false) {
                    setState(() {
                      _copyStarted = true;
                      _startTimer();
                      _saveConfigToFile();
                    });
                  }
                }
              },
              child: Text(_copyStarted ? 'Stop Copy' : 'Start Copy'),
            ),
          ],
        ),
      ),
    );
  }
}

class ThemeNotifier extends ChangeNotifier {
  bool _darkMode = true;
  bool get darkMode => _darkMode;
  void toggleTheme() {
    _darkMode = !_darkMode;
    notifyListeners();
  }
}

class LogScreen extends StatelessWidget {
  final List<LogItem> logs;

  const LogScreen({super.key, required this.logs});

  @override
  Widget build(BuildContext context) {
    // Group logs by date
    Map<String, List<LogItem>> groupedLogs = {};
    for (var log in logs) {
      String date = DateFormat('yyyy-MM-dd').format(log.copyDate);
      if (!groupedLogs.containsKey(date)) {
        groupedLogs[date] = [];
      }
      groupedLogs[date]!.add(log);
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Log'),
      ),
      body: ListView.builder(
        itemCount: groupedLogs.length,
        itemBuilder: (context, index) {
          String date = groupedLogs.keys.elementAt(index);
          List<LogItem> logsForDate = groupedLogs[date]!;
          return _buildExpansionTile(date, logsForDate);
        },
      ),
    );
  }

  Widget _buildExpansionTile(String date, List<LogItem> logsForDate) {
    return ExpansionTile(
      title: Text(date),
      children: logsForDate.map((logItem) {
        String statusText = logItem.copyStatus == CopyStatus.skipped
            ? 'Skipped'
            : logItem.copyStatus == CopyStatus.success
                ? 'Success'
                : 'Failed';
        return ListTile(
          title: Text(logItem.sourcePath),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Copied on: ${DateFormat('yyyy-MM-dd HH:mm').format(logItem.copyDate)} - Status: $statusText',
              ),
              if (logItem.copyStatus == CopyStatus.failed &&
                  logItem.errorMessage != null)
                Text(
                  'Error: ${logItem.errorMessage}',
                  style: const TextStyle(
                      color: Colors.red), // Style the error message
                ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class LogItem {
  final String sourcePath;
  final DateTime copyDate;
  final CopyStatus copyStatus; // New field
  final String? errorMessage;

  // Constructor
  const LogItem({
    required this.sourcePath,
    required this.copyDate,
    required this.copyStatus,
    this.errorMessage,
  });

  // Convert LogItem object to JSON format
  Map<String, dynamic> toJson() {
    return {
      'sourcePath': sourcePath,
      'copyDate': copyDate.toIso8601String(),
      'copyStatus': copyStatus.toString().split('.').last,
      'errorMessage': errorMessage,
    };
  }

  // Convert JSON format to LogItem object
  factory LogItem.fromJson(Map<String, dynamic> json) {
    return LogItem(
      sourcePath: json['sourcePath'],
      copyDate: DateTime.parse(json['copyDate']),
      copyStatus: CopyStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['copyStatus'],
      ),
      errorMessage: json['errorMessage'],
    );
  }
}
