# AutoCopy Memory Optimization Guide

## Overview
This document outlines the memory leak issues identified in the AutoCopy Flutter application and the solutions implemented to prevent high RAM usage during long-running operations.

## Identified Memory Leak Sources

### 1. **Unbounded Log Growth** (Critical)
**Problem**: The `_logs` list grew indefinitely without any cleanup mechanism, causing memory usage to increase continuously over time.

**Solution**: 
- Implemented automatic log cleanup with configurable limits
- Added `maxLogEntries` (1000) and `logCleanupThreshold` (1200) constants
- Automatic cleanup triggers when logs exceed threshold
- Only most recent logs are kept in memory and saved to file

### 2. **Timer Management Issues** (High)
**Problem**: Potential timer leaks in scheduling logic where timers might not be properly disposed.

**Solution**:
- Added proper timer disposal in `dispose()` method
- Implemented `_stopMemoryCleanupTimer()` method
- Added null checks before timer operations
- Ensured all timers are cancelled when widget is disposed

### 3. **File I/O Operations** (Medium)
**Problem**: Frequent file operations without proper resource management and error handling.

**Solution**:
- Added try-catch blocks around all file operations
- Implemented batched log saving instead of saving after each operation
- Limited file sizes by only saving recent logs
- Added proper error handling with `debugPrint` for logging

### 4. **State Management** (Medium)
**Problem**: Multiple `setState()` calls and potential widget rebuilds causing memory pressure.

**Solution**:
- Added `mounted` checks before `setState()` calls
- Reduced unnecessary state updates
- Implemented early exit conditions in copy operations
- Added proper lifecycle management

### 5. **Stream/Directory Listing** (Medium)
**Problem**: `await for` loops that may not be properly disposed and could accumulate memory.

**Solution**:
- Added cancellation checks in directory traversal loops
- Implemented yield control with `Future.delayed()` to prevent blocking
- Added early exit conditions when copy is cancelled
- Better resource management for directory streams

## Implementation Details

### Memory Cleanup Timer
```dart
// Constants for memory management
static const int maxLogEntries = 1000;
static const int logCleanupThreshold = 1200;
static const Duration memoryCleanupInterval = Duration(minutes: 30);

// Automatic cleanup every 30 minutes
void _startMemoryCleanupTimer() {
  _memoryCleanupTimer = Timer.periodic(memoryCleanupInterval, (timer) {
    _cleanupLogs();
  });
}
```

### Log Cleanup Logic
```dart
void _cleanupLogs() {
  if (_logs.length > logCleanupThreshold) {
    final logsToKeep = _logs.length - maxLogEntries;
    _logs.removeRange(0, logsToKeep);
    _saveLogsToFile();
  }
}
```

### Optimized File Operations
```dart
Future<void> _saveLogsToFile() async {
  try {
    // Only save recent logs to prevent huge files
    final logsToSave = _logs.length > maxLogEntries 
        ? _logs.sublist(_logs.length - maxLogEntries)
        : _logs;
    
    final jsonString = json.encode(logsToSave.map((log) => log.toJson()).toList());
    await File('logs.json').writeAsString(jsonString);
  } catch (e) {
    debugPrint('Error saving logs: $e');
  }
}
```

### Copy Operation Optimizations
```dart
Future<void> _copyFiles() async {
  if (!_copyStarted) return; // Early exit
  
  await for (var entity in entities) {
    if (!_copyStarted) break; // Cancellation check
    await _copyEntity(entity, destinationDir, "", overwrite: _overwrite);
    
    // Yield control to prevent blocking UI
    await Future.delayed(const Duration(milliseconds: 1));
  }
  
  // Batch save logs at the end
  await _saveLogsToFile();
}
```

## Performance Improvements

### Before Optimization
- Logs grew indefinitely (could reach 100MB+ in memory)
- No timer cleanup (potential memory leaks)
- File operations on every log entry
- Blocking UI during large copy operations
- No cancellation support

### After Optimization
- Logs limited to 1000 entries (approximately 1-2MB max)
- Automatic cleanup every 30 minutes
- Batched file operations
- Non-blocking copy operations with yield control
- Proper cancellation support
- Memory usage monitoring

## Monitoring and Testing

### Memory Usage Testing
Run the included test suite to verify memory optimizations:
```bash
flutter test test/memory_test.dart
```

### Performance Monitoring
The app now includes:
- Automatic log cleanup
- Memory-efficient file operations
- Proper resource disposal
- Error handling and recovery

### Recommended Monitoring
1. **Log File Size**: Should not exceed 2-3MB
2. **Memory Usage**: Should stabilize after initial operations
3. **Timer Count**: Should not increase over time
4. **UI Responsiveness**: Should remain smooth during copy operations

## Configuration Options

You can adjust memory management settings by modifying these constants:

```dart
static const int maxLogEntries = 1000;        // Max logs in memory
static const int logCleanupThreshold = 1200;  // Cleanup trigger point
static const Duration memoryCleanupInterval = Duration(minutes: 30); // Cleanup frequency
```

## Best Practices for Long-Running Operations

1. **Regular Monitoring**: Check memory usage periodically
2. **Log Rotation**: Implement external log rotation if needed
3. **Resource Cleanup**: Always dispose timers and streams
4. **Error Handling**: Implement robust error handling
5. **UI Responsiveness**: Use yield control in long operations
6. **Cancellation Support**: Allow users to cancel long operations

## Troubleshooting

### High Memory Usage
1. Check log file size
2. Verify cleanup timer is running
3. Monitor for timer leaks
4. Check for uncaught exceptions

### Performance Issues
1. Reduce cleanup interval
2. Lower maxLogEntries limit
3. Implement more aggressive cleanup
4. Add more yield points in copy operations

### File Operation Errors
1. Check file permissions
2. Verify disk space
3. Monitor file handle leaks
4. Implement retry logic

## Future Improvements

1. **Database Storage**: Consider SQLite for better log management
2. **Streaming Operations**: Implement streaming for very large files
3. **Background Processing**: Move heavy operations to isolates
4. **Memory Profiling**: Add runtime memory monitoring
5. **Configurable Limits**: Make limits user-configurable
