import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:autocopy/main.dart';
import 'dart:io';
import 'dart:convert';

void main() {
  group('Memory Management Tests', () {
    late Directory tempDir;
    late File configFile;
    late File logsFile;

    setUp(() async {
      // Create temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('autocopy_test');
      configFile = File('${tempDir.path}/config.json');
      logsFile = File('${tempDir.path}/logs.json');
    });

    tearDown(() async {
      // Clean up temporary files
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    testWidgets('App should start without memory leaks', (WidgetTester tester) async {
      await tester.pumpWidget(const AutoCopyApp());
      await tester.pumpAndSettle();

      // Verify the app starts correctly
      expect(find.text('AutoCopy'), findsOneWidget);
      expect(find.text('Task Title'), findsOneWidget);
    });

    test('Log cleanup should limit memory usage', () {
      // Create a large number of log items
      final logs = <LogItem>[];
      for (int i = 0; i < 2000; i++) {
        logs.add(LogItem(
          sourcePath: 'test_path_$i',
          copyDate: DateTime.now(),
          copyStatus: CopyStatus.success,
        ));
      }

      // Simulate log cleanup (this would be done by _cleanupLogs method)
      const maxLogEntries = 1000;
      const logCleanupThreshold = 1200;
      
      if (logs.length > logCleanupThreshold) {
        final logsToKeep = logs.length - maxLogEntries;
        logs.removeRange(0, logsToKeep);
      }

      // Verify logs are limited
      expect(logs.length, equals(maxLogEntries));
    });

    test('Log file should be limited in size', () async {
      // Create test logs
      final testLogs = <Map<String, dynamic>>[];
      for (int i = 0; i < 1500; i++) {
        testLogs.add({
          'sourcePath': 'test_path_$i',
          'copyDate': DateTime.now().toIso8601String(),
          'copyStatus': 'success',
          'errorMessage': null,
        });
      }

      // Write logs to file
      await logsFile.writeAsString(json.encode(testLogs));

      // Read and verify file size limitation
      final jsonString = await logsFile.readAsString();
      final List<dynamic> logsJson = json.decode(jsonString);
      
      // In real implementation, only maxLogEntries should be saved
      expect(logsJson.length, equals(1500)); // This test shows the issue
      
      // After implementing our fix, this should be limited
      const maxLogEntries = 1000;
      final logsToSave = logsJson.length > maxLogEntries 
          ? logsJson.sublist(logsJson.length - maxLogEntries)
          : logsJson;
      
      expect(logsToSave.length, equals(maxLogEntries));
    });

    test('LogItem should serialize/deserialize correctly', () {
      final logItem = LogItem(
        sourcePath: 'test/path',
        copyDate: DateTime.now(),
        copyStatus: CopyStatus.success,
        errorMessage: null,
      );

      final json = logItem.toJson();
      final deserializedLogItem = LogItem.fromJson(json);

      expect(deserializedLogItem.sourcePath, equals(logItem.sourcePath));
      expect(deserializedLogItem.copyStatus, equals(logItem.copyStatus));
    });

    test('AppConfig should serialize correctly', () {
      final config = AppConfig(
        taskTitle: 'Test Task',
        sourcePath: '/test/source',
        destinationPath: '/test/destination',
        overwrite: true,
        scheduledTime: DateTime.now(),
        selectedFrequency: Frequency.daily,
      );

      final json = config.toJson();
      
      expect(json['taskTitle'], equals('Test Task'));
      expect(json['sourcePath'], equals('/test/source'));
      expect(json['destinationPath'], equals('/test/destination'));
      expect(json['overwrite'], equals(true));
      expect(json['selectedFrequency'], equals('daily'));
    });

    testWidgets('Memory cleanup timer should be properly managed', (WidgetTester tester) async {
      await tester.pumpWidget(const AutoCopyApp());
      await tester.pumpAndSettle();

      // Navigate to the main screen
      expect(find.byType(AutoCopyScreen), findsOneWidget);

      // Dispose the widget to test cleanup
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();

      // If we reach here without errors, timers were properly disposed
      expect(true, isTrue);
    });
  });

  group('Performance Tests', () {
    test('Large log list operations should be efficient', () {
      final stopwatch = Stopwatch()..start();
      
      final logs = <LogItem>[];
      for (int i = 0; i < 10000; i++) {
        logs.add(LogItem(
          sourcePath: 'test_path_$i',
          copyDate: DateTime.now(),
          copyStatus: CopyStatus.success,
        ));
      }
      
      stopwatch.stop();
      
      // Should complete in reasonable time (less than 1 second)
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      expect(logs.length, equals(10000));
    });

    test('JSON serialization should be efficient for large datasets', () {
      final logs = <LogItem>[];
      for (int i = 0; i < 1000; i++) {
        logs.add(LogItem(
          sourcePath: 'test_path_$i',
          copyDate: DateTime.now(),
          copyStatus: CopyStatus.success,
        ));
      }

      final stopwatch = Stopwatch()..start();
      final jsonList = logs.map((log) => log.toJson()).toList();
      final jsonString = json.encode(jsonList);
      stopwatch.stop();

      // Should complete in reasonable time
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      expect(jsonString.isNotEmpty, isTrue);
    });
  });
}
